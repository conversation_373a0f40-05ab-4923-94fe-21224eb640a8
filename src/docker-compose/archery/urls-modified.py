from django.urls import include, path
from django.contrib import admin
from common import views
from django.conf import settings

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", include(("sql_api.urls", "sql_api"), namespace="sql_api")),
    path("", include(("sql.urls", "sql"), namespace="sql")),
]

if settings.ENABLE_CAS:  # pragma: no cover
    import django_cas_ng.views

    urlpatterns += [
        path(
            "cas/authenticate/",
            django_cas_ng.views.LoginView.as_view(),
            name="cas-login",
        ),
    ]  # pragma: no cover

if settings.ENABLE_OIDC:  # pragma: no cover
    from common.authenticate.oidc_views import FeishuOIDCCallbackView
    from mozilla_django_oidc.views import OIDCAuthenticationRequestView, OIDCLogoutView

    urlpatterns += [
        path("oidc/authenticate/", OIDCAuthenticationRequestView.as_view(), name="oidc_authentication_init"),
        path("oidc/callback/", FeishuOIDCCallbackView.as_view(), name="oidc_authentication_callback"),
        path("oidc/logout/", OIDCLogoutView.as_view(), name="oidc_logout"),
    ]

if settings.ENABLE_DINGDING:  # pragma: no cover
    urlpatterns += [
        path("dingding/", include("django_auth_dingding.urls")),
    ]

handler400 = views.bad_request
handler403 = views.permission_denied
handler404 = views.page_not_found
handler500 = views.server_error
