from mozilla_django_oidc import auth
from django.core.exceptions import SuspiciousOperation
from common.auth import init_user
import logging
import requests
import json
from django.conf import settings

logger = logging.getLogger("mozilla_django_oidc")


class OIDCAuthenticationBackend(auth.OIDCAuthenticationBackend):

    def get_app_access_token(self):
        """
        获取飞书的 app_access_token，用于调用飞书 API
        参考官方文档：https://open.feishu.cn/document/server-docs/authentication-management/access-token/app_access_token
        """
        try:
            url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
            headers = {"Content-Type": "application/json; charset=utf-8"}
            payload = {
                "app_id": settings.OIDC_RP_CLIENT_ID,
                "app_secret": settings.OIDC_RP_CLIENT_SECRET
            }

            response = requests.post(url, json=payload, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                return result.get("app_access_token")
            else:
                logger.error(f"Failed to get app_access_token: {result}")
                return None
        except Exception as e:
            logger.error(f"Error getting app_access_token: {e}")
            return None

    def get_user_access_token(self, authorization_code):
        """
        使用授权码获取 user_access_token
        参考官方文档：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/authen-v1/oidc-access_token/create
        """
        try:
            app_access_token = self.get_app_access_token()
            if not app_access_token:
                return None

            url = "https://open.feishu.cn/open-apis/authen/v1/oidc/access_token"
            headers = {
                "Content-Type": "application/json; charset=utf-8",
                "Authorization": f"Bearer {app_access_token}"
            }
            payload = {
                "grant_type": "authorization_code",
                "code": authorization_code
            }

            response = requests.post(url, json=payload, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                return {
                    "access_token": data.get("access_token"),
                    "refresh_token": data.get("refresh_token"),
                    "token_type": data.get("token_type", "Bearer"),
                    "expires_in": data.get("expires_in")
                }
            else:
                logger.error(f"Failed to get user_access_token: {result}")
                return None
        except Exception as e:
            logger.error(f"Error getting user_access_token: {e}")
            return None

    def get_feishu_user_info(self, user_access_token):
        """
        使用 user_access_token 获取飞书用户信息
        参考官方文档：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/authen-v1/user_info/get
        """
        try:
            url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
            headers = {
                "Content-Type": "application/json; charset=utf-8",
                "Authorization": f"Bearer {user_access_token}"
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                return result.get("data", {})
            else:
                logger.error(f"Failed to get user info: {result}")
                return None
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    def verify_token(self, token, **kwargs):
        """
        重写 token 验证方法，使用飞书 API 获取更准确的用户信息
        """
        # 首先尝试标准 OIDC 验证
        payload = super().verify_token(token, **kwargs)
        if not payload:
            return None

        # 检查是否启用飞书 API 增强功能
        if not getattr(settings, 'OIDC_USE_FEISHU_API', True):
            return payload

        # 尝试从 session 中获取授权码，用于获取更详细的用户信息
        request = kwargs.get('request')
        if request and hasattr(request, 'session'):
            authorization_code = request.session.get('oidc_authorization_code')
            if authorization_code:
                logger.debug(f"Using authorization code to get enhanced user info")
                # 使用飞书 API 获取更详细的用户信息
                token_info = self.get_user_access_token(authorization_code)
                if token_info:
                    feishu_user_info = self.get_feishu_user_info(token_info['access_token'])
                    if feishu_user_info:
                        # 将飞书 API 返回的信息合并到 payload 中
                        payload.update({
                            'feishu_name': feishu_user_info.get('name'),
                            'feishu_open_id': feishu_user_info.get('open_id'),
                            'feishu_union_id': feishu_user_info.get('union_id'),
                            'feishu_user_id': feishu_user_info.get('user_id'),
                            'feishu_avatar_url': feishu_user_info.get('avatar_url'),
                            'feishu_tenant_key': feishu_user_info.get('tenant_key'),
                            'feishu_employee_no': feishu_user_info.get('employee_no'),
                            'feishu_mobile': feishu_user_info.get('mobile'),
                        })
                        logger.info(f"Enhanced user info with Feishu API: {feishu_user_info}")
                    else:
                        logger.warning("Failed to get user info from Feishu API, using standard OIDC claims")
                else:
                    logger.warning("Failed to get user access token from Feishu API")

        return payload

    def create_user(self, claims):
        """Return object for a newly created user account."""
        logger.debug(f"Creating user with claims: {claims}")

        email = claims.get("email")
        # 优先使用飞书 API 返回的信息，回退到标准 OIDC 字段
        username = (claims.get("preferred_username") or
                   claims.get("email") or
                   claims.get("sub"))
        display = (claims.get("feishu_name") or
                  claims.get("name"))

        # 获取飞书特有的字段
        open_id = (claims.get("feishu_open_id") or
                  claims.get("open_id") or
                  claims.get("sub"))
        union_id = claims.get("feishu_union_id")
        user_id = claims.get("feishu_user_id")
        employee_no = claims.get("feishu_employee_no")
        mobile = claims.get("feishu_mobile")

        logger.debug(f"Extracted user info - email: {email}, username: {username}, display: {display}, open_id: {open_id}")

        if not email:
            raise SuspiciousOperation("email should not be empty")
        if not username:
            raise SuspiciousOperation("username (preferred_username, email, or sub) should not be empty")
        if not display:
            raise SuspiciousOperation("name should not be empty")

        # 如果用户名是邮箱格式，提取邮箱的用户名部分
        if "@" in username:
            username = username.split("@")[0]
            logger.debug(f"Extracted username from email: {username}")

        try:
            user = self.UserModel.objects.create_user(
                username, email=email
            )
            user.display = display

            # 保存飞书的各种 ID 字段
            if open_id:
                user.feishu_open_id = open_id

            # 如果有手机号，可以考虑保存（需要确认数据库字段是否存在）
            # if mobile:
            #     user.mobile = mobile

            user.save()
            init_user(user)
            logger.info(f"Successfully created user: {username} ({display}) with feishu_open_id: {open_id}")
            return user
        except Exception as e:
            logger.error(f"Failed to create user {username}: {e}")
            raise

    def describe_user_by_claims(self, claims):
        username = claims.get("preferred_username") or claims.get("email") or claims.get("sub")
        return "username {}".format(username)

    def filter_users_by_claims(self, claims):
        """Return all users matching the username."""
        username = (claims.get("preferred_username") or
                   claims.get("email") or
                   claims.get("sub"))
        open_id = (claims.get("feishu_open_id") or
                  claims.get("open_id") or
                  claims.get("sub"))

        if not username:
            return self.UserModel.objects.none()

        # 如果用户名是邮箱格式，提取邮箱的用户名部分
        if "@" in username:
            username = username.split("@")[0]

        if username == "admin":
            return self.UserModel.objects.none()

        # 优先通过 feishu_open_id 查找用户，如果找不到再通过用户名查找
        if open_id:
            users = self.UserModel.objects.filter(feishu_open_id=open_id)
            if users.exists():
                logger.debug(f"Found existing user by feishu_open_id: {open_id}")
                return users

        logger.debug(f"Searching user by username: {username}")
        return self.UserModel.objects.filter(username__iexact=username)
