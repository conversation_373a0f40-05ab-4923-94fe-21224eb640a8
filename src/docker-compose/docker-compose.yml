version: '3'

services:
  redis:
    image: redis:5
    container_name: redis
    restart: always
    command: redis-server --requirepass 123456
    expose:
      - "6379"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5

  mysql:
    image: mysql:5.7
    container_name: mysql
    restart: always
    ports:
      - "3306:3306"
    volumes:
      - "./mysql/my.cnf:/etc/mysql/my.cnf"
      - "./mysql/datadir:/var/lib/mysql"
    environment:
      MYSQL_DATABASE: archery
      MYSQL_ROOT_PASSWORD: 123456
    healthcheck:
      test: ["CMD", "mysqladmin", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  goinception:
    image: hanchuanchuan/goinception
    container_name: goinception
    restart: always
    ports:
      - "4000:4000"
    volumes:
      - "./inception/config.toml:/etc/config.toml"

  archery:
    # 下方的镜像地址仅为示例, 请前往以下地址确认你需要的版本:
    # dockerhub https://hub.docker.com/r/hhyo/archery
    # github packages https://github.com/hhyo/Archery/pkgs/container/archery
    # 如有需要, 也可以自行build docker 镜像, 替换为自己的镜像
    image: hhyo/archery:v1.12.0
    container_name: archery
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    ports:
      - "9123:9123"
      - "80:80"
      - "443:443"
    volumes:
      - "./archery/settings-modified.py:/opt/archery/archery/settings.py"
      - "./archery/urls-modified.py:/opt/archery/archery/urls.py"
      - "./common/authenticate/oidc_auth.py:/opt/archery/common/authenticate/oidc_auth.py"
      - "./common/authenticate/oidc_views.py:/opt/archery/common/authenticate/oidc_views.py"
      - "./archery/settings.py:/opt/archery/local_settings.py"
      - "./archery/soar.yaml:/etc/soar.yaml"
      - "./archery/docs.md:/opt/archery/docs/docs.md"
      - "./archery/downloads:/opt/archery/downloads"
      - "./archery/sql/migrations:/opt/archery/sql/migrations"
      - "./archery/logs:/opt/archery/logs"
      - "./archery/keys:/opt/archery/keys"
      - "./nginx/nginx.conf:/etc/nginx/nginx.conf"
      - "./nginx/cert:/etc/nginx/cert"
    entrypoint: "bash /opt/archery/src/docker/startup.sh"
    env_file:
      - .env
