# 飞书 OIDC 集成增强功能

## 概述

基于飞书官方文档和参考代码，我们对 Archery 的 OIDC 集成进行了重大增强，提供了更好的用户体验和更丰富的用户信息。

## 主要改进

### 1. 混合认证方案

结合了标准 OIDC 协议和飞书原生 API，提供最佳的兼容性和功能性：

- **标准 OIDC 流程**：保持与 mozilla_django_oidc 的兼容性
- **飞书 API 增强**：使用飞书原生 API 获取更详细的用户信息

### 2. 增强的用户信息

通过飞书 API 获取的额外用户信息：

- `feishu_open_id`: 飞书用户的 OpenID
- `feishu_union_id`: 飞书用户的 UnionID
- `feishu_user_id`: 飞书用户 ID
- `feishu_employee_no`: 员工工号
- `feishu_mobile`: 手机号
- `feishu_avatar_url`: 头像 URL
- `feishu_tenant_key`: 租户标识

### 3. 改进的用户匹配

- 优先通过 `feishu_open_id` 匹配现有用户
- 支持多种用户名来源的回退机制
- 防止重复用户创建

### 4. 增强的错误处理

- 详细的日志记录
- 自定义 OIDC 回调视图
- 更好的错误信息和调试支持

## 配置选项

### 环境变量

```bash
# 基础 OIDC 配置
OIDC_RP_CLIENT_ID=your_feishu_app_id
OIDC_RP_CLIENT_SECRET=your_feishu_app_secret
OIDC_RP_WELLKNOWN_URL=https://anycross.feishu.cn/sso/YOUR_SSO_ID/.well-known/openid-configuration

# 飞书增强功能（可选）
OIDC_USE_FEISHU_API=true  # 是否使用飞书 API 获取增强用户信息
```

### Django 设置

在 `settings.py` 中自动配置：

```python
# 飞书增强功能配置
OIDC_USE_FEISHU_API = env("OIDC_USE_FEISHU_API", default=True)
```

## API 端点

### 飞书官方 API 端点

1. **获取 app_access_token**
   - URL: `https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal`
   - 方法: POST
   - 用途: 获取应用访问令牌

2. **获取 user_access_token**
   - URL: `https://open.feishu.cn/open-apis/authen/v1/oidc/access_token`
   - 方法: POST
   - 用途: 使用授权码获取用户访问令牌

3. **获取用户信息**
   - URL: `https://open.feishu.cn/open-apis/authen/v1/user_info`
   - 方法: GET
   - 用途: 获取详细的用户信息

## 使用方法

### 1. 基本使用

正常的 OIDC 登录流程，系统会自动使用增强功能：

```
用户点击登录 → 飞书授权 → 回调处理 → 用户信息增强 → 登录成功
```

### 2. 测试 API 连接

使用管理命令测试飞书 API 连接：

```bash
# 测试基本连接
python manage.py test_feishu_api

# 测试完整流程（需要授权码）
python manage.py test_feishu_api --authorization-code YOUR_AUTH_CODE
```

### 3. 禁用增强功能

如果需要禁用飞书 API 增强功能，设置环境变量：

```bash
OIDC_USE_FEISHU_API=false
```

## 技术实现

### 认证流程

1. **标准 OIDC 验证**：使用 mozilla_django_oidc 进行基础验证
2. **授权码保存**：在回调时将授权码保存到 session
3. **API 增强**：使用授权码调用飞书 API 获取详细信息
4. **信息合并**：将飞书 API 信息合并到 OIDC claims 中
5. **用户创建/更新**：使用增强信息创建或更新用户

### 关键组件

- `OIDCAuthenticationBackend`: 增强的认证后端
- `FeishuOIDCCallbackView`: 自定义回调视图
- `test_feishu_api`: 测试管理命令

## 故障排除

### 常见问题

1. **500 错误**：检查 `end_session_endpoint` 配置
2. **用户信息不完整**：检查飞书 API 权限配置
3. **重复用户**：检查 `feishu_open_id` 字段配置

### 调试日志

启用详细日志记录：

```python
LOGGING = {
    'loggers': {
        'mozilla_django_oidc': {
            'level': 'DEBUG',
        },
    },
}
```

## 与官方代码的对比

| 特性 | 官方 QR 登录 | 我们的 OIDC 集成 | 优势 |
|------|-------------|-----------------|------|
| 协议标准 | 自定义 API | 标准 OIDC + 飞书 API | 更好的兼容性 |
| 用户信息 | 基础信息 | 增强信息 | 更丰富的用户数据 |
| 错误处理 | 基础 | 增强 | 更好的调试体验 |
| 可配置性 | 固定 | 灵活 | 适应不同需求 |

## 参考文档

- [飞书 OIDC 文档](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/authen-v1/oidc-access_token/create)
- [获取用户访问令牌](https://open.feishu.cn/document/server-docs/authentication-management/access-token/create-2)
- [获取用户信息](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/authen-v1/user_info/get)
