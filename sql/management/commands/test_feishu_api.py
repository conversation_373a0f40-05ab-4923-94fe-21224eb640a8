from django.core.management.base import BaseCommand
from django.conf import settings
from common.authenticate.oidc_auth import OIDCAuthenticationBackend
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '测试飞书 API 连接'

    def add_arguments(self, parser):
        parser.add_argument(
            '--authorization-code',
            type=str,
            help='飞书授权码（用于测试 user_access_token 获取）',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始测试飞书 API 连接...'))
        
        # 检查配置
        if not hasattr(settings, 'OIDC_RP_CLIENT_ID') or not settings.OIDC_RP_CLIENT_ID:
            self.stdout.write(self.style.ERROR('错误: OIDC_RP_CLIENT_ID 未配置'))
            return
            
        if not hasattr(settings, 'OIDC_RP_CLIENT_SECRET') or not settings.OIDC_RP_CLIENT_SECRET:
            self.stdout.write(self.style.ERROR('错误: OIDC_RP_CLIENT_SECRET 未配置'))
            return
        
        backend = OIDCAuthenticationBackend()
        
        # 测试获取 app_access_token
        self.stdout.write('测试获取 app_access_token...')
        app_token = backend.get_app_access_token()
        if app_token:
            self.stdout.write(self.style.SUCCESS(f'✓ 成功获取 app_access_token: {app_token[:20]}...'))
        else:
            self.stdout.write(self.style.ERROR('✗ 获取 app_access_token 失败'))
            return
        
        # 如果提供了授权码，测试获取 user_access_token
        authorization_code = options.get('authorization_code')
        if authorization_code:
            self.stdout.write(f'测试使用授权码获取 user_access_token: {authorization_code[:10]}...')
            token_info = backend.get_user_access_token(authorization_code)
            if token_info:
                self.stdout.write(self.style.SUCCESS(f'✓ 成功获取 user_access_token: {token_info["access_token"][:20]}...'))
                
                # 测试获取用户信息
                self.stdout.write('测试获取用户信息...')
                user_info = backend.get_feishu_user_info(token_info['access_token'])
                if user_info:
                    self.stdout.write(self.style.SUCCESS('✓ 成功获取用户信息:'))
                    for key, value in user_info.items():
                        self.stdout.write(f'  {key}: {value}')
                else:
                    self.stdout.write(self.style.ERROR('✗ 获取用户信息失败'))
            else:
                self.stdout.write(self.style.ERROR('✗ 获取 user_access_token 失败'))
        else:
            self.stdout.write(self.style.WARNING('提示: 使用 --authorization-code 参数可以测试完整的用户信息获取流程'))
        
        self.stdout.write(self.style.SUCCESS('飞书 API 测试完成'))
